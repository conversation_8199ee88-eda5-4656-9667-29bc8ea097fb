import { Box, Text, useStdout } from 'ink';
import { useCallback, useState } from 'react';
import Calendar from './calendar.js';

type DayData = {
  entered: number;
  expected: number;
};

function generateDayData(year: number, month: number): Record<number, DayData> {
  const daysInMonth = new Date(year, month, 0).getDate();
  const data: Record<number, DayData> = {};
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    const weekday = date.getDay();
    const expected = weekday === 0 || weekday === 6 ? 0 : 8;
    const entered =
      weekday === 0 || weekday === 6 ? 0 : Math.floor(Math.random() * 9);
    data[day] = { entered, expected };
  }

  return data;
}

export default function App() {
  const { stdout } = useStdout();

  const currentDate = new Date();
  const [month, setMonth] = useState(currentDate.getMonth() + 1); // GetMonth() returns 0-11
  const [year, setYear] = useState(currentDate.getFullYear());

  const data = generateDayData(year, month);

  const handleMonthChange = useCallback((newMonth: number, newYear: number) => {
    setMonth(newMonth);
    setYear(newYear);
  }, []);

  return (
    <Box flexDirection="column">
      <Text>
        {stdout.columns}x{stdout.rows}
      </Text>
      <Calendar
        dayData={data}
        month={month}
        onMonthChange={handleMonthChange}
        year={year}
      />
    </Box>
  );
}
