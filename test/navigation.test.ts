import test from 'ava';

// Helper functions extracted from calendar.tsx for testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function findLastDayOfWeekInMonth(
  targetYear: number,
  targetMonth: number,
  dayOfWeek: number
): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  // Start from the last day and work backwards
  for (let day = daysInTargetMonth; day >= 1; day--) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  // Fallback to last day if no match found (shouldn't happen)
  return daysInTargetMonth;
}

function findFirstDayOfWeekInMonth(
  targetYear: number,
  targetMonth: number,
  dayOfWeek: number
): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  // Start from the first day and work forwards
  for (let day = 1; day <= daysInTargetMonth; day++) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  // Fallback to first day if no match found (shouldn't happen)
  return 1;
}

function getCurrentDayOfWeek(
  selectedDay: number,
  year: number,
  month: number
): number {
  const date = new Date(year, month - 1, selectedDay);
  return date.getDay();
}

// Core navigation function tests
test('findLastDayOfWeekInMonth works correctly', (t) => {
  // June 2025: last Sunday should be June 29th
  const lastSunday = findLastDayOfWeekInMonth(2025, 6, 0);
  t.is(lastSunday, 29);

  // Verify it's actually a Sunday
  const date = new Date(2025, 5, lastSunday);
  t.is(date.getDay(), 0);
});

test('findFirstDayOfWeekInMonth works correctly', (t) => {
  // August 2025: first Thursday should be August 7th
  const firstThursday = findFirstDayOfWeekInMonth(2025, 8, 4);
  t.is(firstThursday, 7);

  // Verify it's actually a Thursday
  const date = new Date(2025, 7, firstThursday);
  t.is(date.getDay(), 4);
});

test('getCurrentDayOfWeek works correctly', (t) => {
  // July 6, 2025 is a Sunday
  t.is(getCurrentDayOfWeek(6, 2025, 7), 0);

  // July 31, 2025 is a Thursday
  t.is(getCurrentDayOfWeek(31, 2025, 7), 4);
});

test('getDaysInMonth works correctly', (t) => {
  t.is(getDaysInMonth(2025, 7), 31); // July
  t.is(getDaysInMonth(2025, 2), 28); // February non-leap
  t.is(getDaysInMonth(2024, 2), 29); // February leap
});
