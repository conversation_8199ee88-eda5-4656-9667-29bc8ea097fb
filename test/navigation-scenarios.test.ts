import test from 'ava';

// Helper functions for navigation testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function findLastDayOfWeekInMonth(
  targetYear: number,
  targetMonth: number,
  dayOfWeek: number
): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  for (let day = daysInTargetMonth; day >= 1; day--) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  return daysInTargetMonth;
}

function findFirstDayOfWeekInMonth(
  targetYear: number,
  targetMonth: number,
  dayOfWeek: number
): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  for (let day = 1; day <= daysInTargetMonth; day++) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  return 1;
}

function getCurrentDayOfWeek(
  selectedDay: number,
  year: number,
  month: number
): number {
  const date = new Date(year, month - 1, selectedDay);
  return date.getDay();
}

// Simulate month navigation logic
function simulateNavigateToPreviousMonth(
  currentYear: number,
  currentMonth: number,
  currentDay: number
) {
  let newMonth = currentMonth - 1;
  let newYear = currentYear;

  if (newMonth < 1) {
    newMonth = 12;
    newYear = currentYear - 1;
  }

  const currentDayOfWeek = getCurrentDayOfWeek(
    currentDay,
    currentYear,
    currentMonth
  );
  const targetDay = findLastDayOfWeekInMonth(
    newYear,
    newMonth,
    currentDayOfWeek
  );

  return { year: newYear, month: newMonth, day: targetDay };
}

function simulateNavigateToNextMonth(
  currentYear: number,
  currentMonth: number,
  currentDay: number
) {
  let newMonth = currentMonth + 1;
  let newYear = currentYear;

  if (newMonth > 12) {
    newMonth = 1;
    newYear = currentYear + 1;
  }

  const currentDayOfWeek = getCurrentDayOfWeek(
    currentDay,
    currentYear,
    currentMonth
  );
  const targetDay = findFirstDayOfWeekInMonth(
    newYear,
    newMonth,
    currentDayOfWeek
  );

  return { year: newYear, month: newMonth, day: targetDay };
}

// Test specific navigation scenarios mentioned in the requirements
test('July 6th (Sunday) UP navigation goes to last Sunday of June', (t) => {
  const result = simulateNavigateToPreviousMonth(2025, 7, 6);

  t.is(result.year, 2025);
  t.is(result.month, 6);
  t.is(result.day, 29); // June 29, 2025 is the last Sunday

  // Verify both days are Sundays
  const originalDayOfWeek = getCurrentDayOfWeek(6, 2025, 7);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 0); // Sunday
  t.is(targetDayOfWeek, 0); // Sunday
});

test('July 2nd (Wednesday) UP navigation goes to last Wednesday of June', (t) => {
  const result = simulateNavigateToPreviousMonth(2025, 7, 2);

  t.is(result.year, 2025);
  t.is(result.month, 6);
  t.is(result.day, 25); // June 25, 2025 is the last Wednesday

  // Verify both days are Wednesdays
  const originalDayOfWeek = getCurrentDayOfWeek(2, 2025, 7);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 3); // Wednesday
  t.is(targetDayOfWeek, 3); // Wednesday
});

test('July 31st (Thursday) DOWN navigation goes to first Thursday of August', (t) => {
  const result = simulateNavigateToNextMonth(2025, 7, 31);

  t.is(result.year, 2025);
  t.is(result.month, 8);
  t.is(result.day, 7); // August 7, 2025 is the first Thursday

  // Verify both days are Thursdays
  const originalDayOfWeek = getCurrentDayOfWeek(31, 2025, 7);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 4); // Thursday
  t.is(targetDayOfWeek, 4); // Thursday
});

test('February 28th (Friday) DOWN navigation goes to first Friday of March', (t) => {
  const result = simulateNavigateToNextMonth(2025, 2, 28);

  t.is(result.year, 2025);
  t.is(result.month, 3);
  t.is(result.day, 7); // March 7, 2025 is the first Friday

  // Verify both days are Fridays
  const originalDayOfWeek = getCurrentDayOfWeek(28, 2025, 2);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 5); // Friday
  t.is(targetDayOfWeek, 5); // Friday
});

// Test year boundary navigation
test('January 1st (Wednesday) UP navigation goes to last Wednesday of December previous year', (t) => {
  const result = simulateNavigateToPreviousMonth(2025, 1, 1);

  t.is(result.year, 2024);
  t.is(result.month, 12);
  t.is(result.day, 25); // December 25, 2024 is the last Wednesday

  // Verify both days are Wednesdays
  const originalDayOfWeek = getCurrentDayOfWeek(1, 2025, 1);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 3); // Wednesday
  t.is(targetDayOfWeek, 3); // Wednesday
});

test('December 31st (Tuesday) DOWN navigation goes to first Tuesday of January next year', (t) => {
  const result = simulateNavigateToNextMonth(2024, 12, 31);

  t.is(result.year, 2025);
  t.is(result.month, 1);
  t.is(result.day, 7); // January 7, 2025 is the first Tuesday

  // Verify both days are Tuesdays
  const originalDayOfWeek = getCurrentDayOfWeek(31, 2024, 12);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 2); // Tuesday
  t.is(targetDayOfWeek, 2); // Tuesday
});

// Test leap year scenarios
test('February 29th (Thursday) leap year DOWN navigation goes to first Thursday of March', (t) => {
  const result = simulateNavigateToNextMonth(2024, 2, 29);

  t.is(result.year, 2024);
  t.is(result.month, 3);
  t.is(result.day, 7); // March 7, 2024 is the first Thursday

  // Verify both days are Thursdays
  const originalDayOfWeek = getCurrentDayOfWeek(29, 2024, 2);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 4); // Thursday (Feb 29, 2024 is actually Thursday)
  t.is(targetDayOfWeek, 4); // Thursday
});

test('March 1st (Friday) UP navigation goes to last Friday of February in leap year', (t) => {
  const result = simulateNavigateToPreviousMonth(2024, 3, 1);

  t.is(result.year, 2024);
  t.is(result.month, 2);
  t.is(result.day, 23); // February 23, 2024 is the last Friday

  // Verify both days are Fridays
  const originalDayOfWeek = getCurrentDayOfWeek(1, 2024, 3);
  const targetDayOfWeek = getCurrentDayOfWeek(
    result.day,
    result.year,
    result.month
  );
  t.is(originalDayOfWeek, 5); // Friday
  t.is(targetDayOfWeek, 5); // Friday
});

// Test all days of the week
test('Navigation preserves all days of the week correctly', (t) => {
  const dayNames = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  // Test a week in July 2025: July 6-12 (Sunday through Saturday)
  for (let day = 6; day <= 12; day++) {
    const dayOfWeek = getCurrentDayOfWeek(day, 2025, 7);
    const dayName = dayNames[dayOfWeek];

    // Test UP navigation (to June)
    const upResult = simulateNavigateToPreviousMonth(2025, 7, day);
    const upDayOfWeek = getCurrentDayOfWeek(
      upResult.day,
      upResult.year,
      upResult.month
    );
    t.is(
      upDayOfWeek,
      dayOfWeek,
      `UP navigation from July ${day} (${dayName}) should preserve day of week`
    );

    // Test DOWN navigation (to August)
    const downResult = simulateNavigateToNextMonth(2025, 7, day);
    const downDayOfWeek = getCurrentDayOfWeek(
      downResult.day,
      downResult.year,
      downResult.month
    );
    t.is(
      downDayOfWeek,
      dayOfWeek,
      `DOWN navigation from July ${day} (${dayName}) should preserve day of week`
    );
  }
});

// Test edge case: month that starts and ends on same day of week
test('Navigation works correctly for months with same start/end day of week', (t) => {
  // Find a month that starts and ends on the same day of week
  // March 2025 starts on Saturday (day 1) and ends on Monday (day 31)
  // Let's test February 2025 which starts on Saturday and ends on Friday

  const firstDay = simulateNavigateToNextMonth(2025, 1, 31); // Jan 31 (Friday) -> Feb
  t.is(firstDay.month, 2);
  t.is(getCurrentDayOfWeek(firstDay.day, firstDay.year, firstDay.month), 5); // Should be Friday

  const lastDay = simulateNavigateToPreviousMonth(2025, 3, 1); // Mar 1 (Saturday) -> Feb
  t.is(lastDay.month, 2);
  t.is(getCurrentDayOfWeek(lastDay.day, lastDay.year, lastDay.month), 6); // Should be Saturday
});
