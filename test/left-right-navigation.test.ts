import test from 'ava';

// Helper functions for navigation testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}



// Simulate left navigation logic
function simulateNavigateLeft(currentYear: number, currentMonth: number, currentDay: number) {
  // Check if we're on the first day of the month
  if (currentDay === 1) {
    let newMonth = currentMonth - 1;
    let newYear = currentYear;

    if (newMonth < 1) {
      newMonth = 12;
      newYear = currentYear - 1;
    }

    const lastDayOfPreviousMonth = getDaysInMonth(newYear, newMonth);
    return { year: newYear, month: newMonth, day: lastDayOfPreviousMonth };
  }

  // Otherwise, just move to previous day
  return { year: currentYear, month: currentMonth, day: currentDay - 1 };
}

// Simulate right navigation logic
function simulateNavigateRight(currentYear: number, currentMonth: number, currentDay: number) {
  const daysInCurrentMonth = getDaysInMonth(currentYear, currentMonth);

  // Check if we're on the last day of the month
  if (currentDay === daysInCurrentMonth) {
    let newMonth = currentMonth + 1;
    let newYear = currentYear;

    if (newMonth > 12) {
      newMonth = 1;
      newYear = currentYear + 1;
    }

    return { year: newYear, month: newMonth, day: 1 };
  }

  // Otherwise, just move to next day
  return { year: currentYear, month: currentMonth, day: currentDay + 1 };
}

// Test left navigation from first day of month
test('LEFT from July 1st goes to June 30th (last day of June)', t => {
  const result = simulateNavigateLeft(2025, 7, 1);

  t.is(result.year, 2025);
  t.is(result.month, 6);
  t.is(result.day, 30); // June 30, 2025 is the last day of June
});

test('LEFT from January 1st goes to December 31st previous year', t => {
  const result = simulateNavigateLeft(2025, 1, 1);

  t.is(result.year, 2024);
  t.is(result.month, 12);
  t.is(result.day, 31); // December 31, 2024 is the last day of December
});

test('LEFT from February 1st goes to January 31st', t => {
  const result = simulateNavigateLeft(2025, 2, 1);

  t.is(result.year, 2025);
  t.is(result.month, 1);
  t.is(result.day, 31); // January 31, 2025 is the last day of January
});

// Test right navigation from last day of month
test('RIGHT from July 31st goes to August 1st (first day of August)', t => {
  const result = simulateNavigateRight(2025, 7, 31);

  t.is(result.year, 2025);
  t.is(result.month, 8);
  t.is(result.day, 1); // August 1, 2025 is the first day of August
});

test('RIGHT from December 31st goes to January 1st next year', t => {
  const result = simulateNavigateRight(2024, 12, 31);

  t.is(result.year, 2025);
  t.is(result.month, 1);
  t.is(result.day, 1); // January 1, 2025 is the first day of January
});

test('RIGHT from February 28th goes to March 1st', t => {
  const result = simulateNavigateRight(2025, 2, 28);

  t.is(result.year, 2025);
  t.is(result.month, 3);
  t.is(result.day, 1); // March 1, 2025 is the first day of March
});

test('RIGHT from February 29th (leap year) goes to March 1st', t => {
  const result = simulateNavigateRight(2024, 2, 29);

  t.is(result.year, 2024);
  t.is(result.month, 3);
  t.is(result.day, 1); // March 1, 2024 is the first day of March
});

// Test normal within-month navigation
test('LEFT from July 15th goes to July 14th (normal navigation)', t => {
  const result = simulateNavigateLeft(2025, 7, 15);

  t.is(result.year, 2025);
  t.is(result.month, 7);
  t.is(result.day, 14);
});

test('RIGHT from July 15th goes to July 16th (normal navigation)', t => {
  const result = simulateNavigateRight(2025, 7, 15);

  t.is(result.year, 2025);
  t.is(result.month, 7);
  t.is(result.day, 16);
});

// Test edge cases with different month lengths
test('LEFT from March 1st goes to February 28th (non-leap year)', t => {
  const result = simulateNavigateLeft(2025, 3, 1);

  t.is(result.year, 2025);
  t.is(result.month, 2);
  t.is(result.day, 28); // February 28, 2025 is the last day of February (non-leap year)
});

test('RIGHT from April 30th goes to May 1st', t => {
  const result = simulateNavigateRight(2025, 4, 30);

  t.is(result.year, 2025);
  t.is(result.month, 5);
  t.is(result.day, 1); // May 1, 2025 is the first day of May
});

test('LEFT from March 1st goes to February 29th (leap year)', t => {
  const result = simulateNavigateLeft(2024, 3, 1);

  t.is(result.year, 2024);
  t.is(result.month, 2);
  t.is(result.day, 29); // February 29, 2024 is the last day of February (leap year)
});
